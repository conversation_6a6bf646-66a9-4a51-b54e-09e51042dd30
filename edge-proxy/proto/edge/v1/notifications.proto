syntax = "proto3";

package edge.v1;

option go_package = "github.com/EPIKio/myepikV2/edge/proto/edge/v1;edgev1";

// DeviceRequest represents a request to proxy to an edge device.
message DeviceRequest {
  // Serial number of the device
  string serial_number = 1;

  // Path to forward to the device (e.g., "/dcavgping")
  string path = 2;
}

// Response from the edge device
message DeviceResponse {
  // HTTP status code from the device
  int32 status_code = 1;

  // Raw response body from the device
  bytes body = 2;

  // HTTP headers from the device's response
  map<string, string> headers = 3;
}

// EdgeDeviceProxy service definition
service EdgeDeviceProxy {
  // HandleRequest handles proxying requests to edge devices
  rpc HandleRequest(DeviceRequest) returns (DeviceResponse) {}
}
