package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"

	server "github.com/EPIKio/myepikV2/edge/pkg/app"
	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	boxhttp "github.com/EPIKio/myepikV2/edge/pkg/boxes/http"
	"github.com/EPIKio/myepikV2/edge/pkg/config"
	"github.com/EPIKio/myepikV2/edge/pkg/priority"
	"github.com/EPIKio/myepikV2/edge/pkg/proxy"
	"github.com/EPIKio/myepikV2/edge/pkg/queue"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func main() {
	// Create base context with cancellation
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle shutdown signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sig<PERSON>han, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sig<PERSON>han
		cancel()
	}()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Create MongoDB repository with context
	boxRepo, err := boxes.NewBoxRepository(ctx, cfg.MongoURI, cfg.MongoDB)
	if err != nil {
		log.Fatalf("Failed to create box repository: %v", err)
	}

	// Create queue manager
	queueManager, err := queue.NewJetStreamQueueManager(
		cfg.NATSURL,
		cfg.MongoURI,
		cfg.MongoDB,
		cfg.QueueConfig.StreamName,
		cfg.QueueConfig.ConsumerName,
	)
	if err != nil {
		log.Fatalf("Failed to create queue manager: %v", err)
	}

	// Create priority classifier
	classifier := priority.NewRequestClassifier()

	// Create request processor
	client := boxhttp.NewDefaultClient()
	proxyServer, err := proxy.NewServer(client, boxRepo)
	if err != nil {
		log.Fatalf("Failed to create proxy server: %v", err)
	}

	// Get MongoDB database for processor (we need to modify the repository to expose this)
	// For now, we'll create a new connection
	mongoClient, err := mongo.Connect(ctx, options.Client().ApplyURI(cfg.MongoURI))
	if err != nil {
		log.Fatalf("Failed to connect to MongoDB for processor: %v", err)
	}
	db := mongoClient.Database(cfg.MongoDB)

	processor := queue.NewEdgeRequestProcessor(proxyServer, db)

	// Start queue processing
	if err := queueManager.StartProcessing(ctx, processor); err != nil {
		log.Fatalf("Failed to start queue processing: %v", err)
	}

	// Create server with all dependencies
	srv, err := server.New(server.Config{
		Port:          cfg.ServerPort,
		BoxRepository: boxRepo,
		QueueManager:  queueManager,
		Classifier:    classifier,
	})
	if err != nil {
		log.Fatalf("Failed to create server: %v", err)
	}

	// Run server
	errChan := make(chan error, 1)
	go func() {
		errChan <- srv.Run()
	}()

	// Wait for either context cancellation or server error
	select {
	case <-ctx.Done():
		// Initiate graceful shutdown
		log.Println("Shutting down gracefully...")
		srv.Stop()
		queueManager.Stop()
		mongoClient.Disconnect(context.Background())
	case err := <-errChan:
		if err != nil {
			log.Fatalf("Server error: %v", err)
		}
	}
}
