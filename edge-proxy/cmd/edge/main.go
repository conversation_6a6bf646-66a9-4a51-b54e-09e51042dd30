package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"

	server "github.com/EPIKio/myepikV2/edge/pkg/app"
	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	"github.com/EPIKio/myepikV2/edge/pkg/config"
)

func main() {
	// Create base context with cancellation
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle shutdown signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sig<PERSON>han, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sig<PERSON>han
		cancel()
	}()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Create MongoDB repository with context
	boxRepo, err := boxes.NewBoxRepository(ctx, cfg.MongoURI, cfg.MongoDB)
	if err != nil {
		log.Fatalf("Failed to create box repository: %v", err)
	}

	// Create server with all dependencies
	srv, err := server.New(server.Config{
		Port:          cfg.ServerPort,
		BoxRepository: boxRepo, // Remove dereferencing operator *
	})
	if err != nil {
		log.Fatalf("Failed to create server: %v", err)
	}

	// Run server
	errChan := make(chan error, 1)
	go func() {
		errChan <- srv.Run()
	}()

	// Wait for either context cancellation or server error
	select {
	case <-ctx.Done():
		// Initiate graceful shutdown
		srv.Stop()
	case err := <-errChan:
		if err != nil {
			log.Fatalf("Server error: %v", err)
		}
	}
}
