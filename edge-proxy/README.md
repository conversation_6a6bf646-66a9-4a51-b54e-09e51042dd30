# Edge Device Go Endpoints (EDGE) Proxy

A gRPC service that orchestrates complex device operations and proxies HTTP
requests to edge devices across multiple datacenters through VPN tunnels.
Part of the myepikV2 monorepo.

## Deployment
Automatically deployed to Kubernetes via GitHub Actions when changes are merged
to main.

## Features

- Complex device operation orchestration with multi-step workflows
- MongoDB-based address lookup with datacenter-specific VPN translation
- Support for multiple datacenter locations (LA, NY, CH, AT, DA/DL/DAL)
- Secure communication via gRPC and VPN tunnels
- Scalable handling of thousands of field devices
- Configurable through environment variables
- Graceful shutdown handling

## Device Resolution

### MongoDB Lookup
Resolves device addresses using MongoDB, returning:
- VPN Address
- Datacenter Information

## Architecture

### Components
- **gRPC Service**: Handles incoming requests from web backends
- **Request Orchestrator**: Manages complex multi-step device operations
- **Box Repository**: MongoDB-backed device lookup service
- **HTTP Client**: Configurable client with retry logic
- **Proxy Logic**: Handles datacenter-specific VPN translation

### Data Flow
```mermaid
sequenceDiagram
    participant B as Web Backend
    participant E as EDGE Proxy
    participant M as MongoDB
    participant D as Edge Device

    B->>E: gRPC Request
    E->>M: Lookup Device
    M-->>E: VPN IP + Datacenter
    E->>E: Translate VPN IP
    E->>D: HTTP Request via VPN
    D-->>E: HTTP Response
    E->>E: Process Response
    E-->>B: gRPC Response
```

## Configuration

Environment variables:
```
EDGE_PORT=50051        # gRPC server port (default)
EDGE_MONGO_URI=mongodb://localhost:27017  # MongoDB connection URI
EDGE_MONGO_DB=epikFax  # MongoDB database name
```

## API

### gRPC Service

```protobuf
service EdgeDeviceProxy {
  rpc HandleRequest(DeviceRequest) returns (DeviceResponse);
}

message DeviceRequest {
  string serial_number = 1;
  string path = 2;
}

message DeviceResponse {
  int32 status_code = 1;
  bytes body = 2;
  map<string, string> headers = 3;
}
```

### Service Ports

- Restbin: 9988 (Main HTTP service)
- Watchguard: 9081 (Monitoring/repair agent)

## Development

### Prerequisites

- Go 1.23.3+
- protoc compiler
- gRPC tools

### Testing with grpcurl

1. Install grpcurl:
```bash
# MacOS
brew install grpcurl

# Linux
go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest

# Windows
# Download latest release from https://github.com/fullstorydev/grpcurl/releases
# Add the executable to your PATH
```

2. Send a test request:
```bash
grpcurl -plaintext -d '{"serial_number": "000199140006", "path": "/dcavgping"}' ^
  localhost:50051 edge.v1.EdgeDeviceProxy/HandleRequest
```

### Running Tests
```bash
go test -v ./...  # -v flag required to show BDD context in test failures
```

### CI/CD

CI runs automatically via GitHub Actions on pull requests and merges to main.
See `.github/workflows/go.yml` for the configuration.

### Deployment
Automatic deployment to Kubernetes is triggered on pushes to main. The service:
- Builds using the Dockerfile in `/edge`
- Pushes to epikedge/edge-proxy Docker registry
- Deploys via kubectl to the cluster

See `.github/workflows/deploy.yml` for details.

## VPN Address Translation

| Datacenter | 10.15.x.x | 10.64.x.x |
|------------|-----------|-----------|
| LA         | No change | No change |
| CH         | 10.18.x.x | 10.66.x.x |
| NY         | 10.19.x.x | 10.67.x.x |
| AT         | 10.20.x.x | 10.68.x.x |
| DA/DL/DAL  | 10.21.x.x | 10.69.x.x |

