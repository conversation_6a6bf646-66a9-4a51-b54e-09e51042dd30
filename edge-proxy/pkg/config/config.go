package config

import (
	"fmt"
	"log"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

const (
	defaultPort     = "50051"
	defaultMongoURI = "mongodb://localhost:27017"
	defaultMongoDB  = "epikFax"
)

// Config holds all configuration values.
type Config struct {
	ServerPort int
	MongoURI   string
	MongoDB    string
}

// getEnv retrieves an environment variable or returns the default value.
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// Load reads configuration from .env file and environment.
func Load() (*Config, error) {
	// Load .env file if it exists.
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: No .env file found, using system environment variables.")
	}

	// Get and parse port number.
	portNum, err := strconv.Atoi(getEnv("EDGE_PORT", defaultPort))
	if err != nil {
		return nil, fmt.Errorf("invalid port number: %v", err)
	}

	return &Config{
		ServerPort: portNum,
		MongoURI:   getEnv("EDGE_MONGO_URI", defaultMongoURI),
	}, nil
}
