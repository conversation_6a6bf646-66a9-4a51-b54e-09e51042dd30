package config

import (
	"fmt"
	"log"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

const (
	defaultPort     = "50051"
	defaultMongoURI = "mongodb://localhost:27017"
	defaultMongoDB  = "epikFax"
	defaultNATSURL  = "nats://localhost:4222"
)

// Config holds all configuration values.
type Config struct {
	ServerPort  int
	MongoURI    string
	MongoDB     string
	NATSURL     string
	QueueConfig QueueConfig
}

// QueueConfig holds queue-specific configuration
type QueueConfig struct {
	StreamName        string
	ConsumerName      string
	MaxRetries        int
	AckTimeout        int // seconds
	ProcessingTimeout int // seconds
}

// getEnv retrieves an environment variable or returns the default value.
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// Load reads configuration from .env file and environment.
func Load() (*Config, error) {
	// Load .env file if it exists.
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: No .env file found, using system environment variables.")
	}

	// Get and parse port number.
	portNum, err := strconv.Atoi(getEnv("EDGE_PORT", defaultPort))
	if err != nil {
		return nil, fmt.Errorf("invalid port number: %v", err)
	}

	// Parse queue configuration
	maxRetries, err := strconv.Atoi(getEnv("EDGE_QUEUE_MAX_RETRIES", "3"))
	if err != nil {
		return nil, fmt.Errorf("invalid max retries: %v", err)
	}

	ackTimeout, err := strconv.Atoi(getEnv("EDGE_QUEUE_ACK_TIMEOUT", "30"))
	if err != nil {
		return nil, fmt.Errorf("invalid ack timeout: %v", err)
	}

	processingTimeout, err := strconv.Atoi(getEnv("EDGE_QUEUE_PROCESSING_TIMEOUT", "300"))
	if err != nil {
		return nil, fmt.Errorf("invalid processing timeout: %v", err)
	}

	return &Config{
		ServerPort: portNum,
		MongoURI:   getEnv("EDGE_MONGO_URI", defaultMongoURI),
		MongoDB:    getEnv("EDGE_MONGO_DB", defaultMongoDB),
		NATSURL:    getEnv("EDGE_NATS_URL", defaultNATSURL),
		QueueConfig: QueueConfig{
			StreamName:        getEnv("EDGE_QUEUE_STREAM", "edge-requests"),
			ConsumerName:      getEnv("EDGE_QUEUE_CONSUMER", "edge-processor"),
			MaxRetries:        maxRetries,
			AckTimeout:        ackTimeout,
			ProcessingTimeout: processingTimeout,
		},
	}, nil
}
