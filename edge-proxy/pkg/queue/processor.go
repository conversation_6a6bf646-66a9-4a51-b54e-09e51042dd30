package queue

import (
	"context"
	"fmt"
	"time"

	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	"github.com/EPIKio/myepikV2/edge/pkg/proxy"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// EdgeRequestProcessor implements RequestProcessor for edge device requests
type EdgeRequestProcessor struct {
	proxyServer *proxy.Server
	db          *mongo.Database
}

// NewEdgeRequestProcessor creates a new processor for edge device requests
func NewEdgeRequestProcessor(proxyServer *proxy.Server, db *mongo.Database) *EdgeRequestProcessor {
	return &EdgeRequestProcessor{
		proxyServer: proxyServer,
		db:          db,
	}
}

// ProcessRequest processes a queued request by proxying it to the edge device
func (p *EdgeRequestProcessor) ProcessRequest(ctx context.Context, req *QueuedRequest) error {
	// Add processing timeout
	ctx, cancel := context.WithTimeout(ctx, 5*time.Minute)
	defer cancel()

	// Update request status to processing with start time
	if err := p.updateRequestProcessing(ctx, req); err != nil {
		return fmt.Errorf("failed to update request status: %w", err)
	}

	// Use the existing proxy server to handle the request
	statusCode, body, headers, err := p.proxyServer.ProxyRequest(ctx, req.SerialNumber, req.Path)
	if err != nil {
		// Update request with error information
		if updateErr := p.updateRequestError(ctx, req, err); updateErr != nil {
			return fmt.Errorf("proxy error: %v, update error: %w", err, updateErr)
		}
		return fmt.Errorf("proxy request failed: %w", err)
	}

	// Update request with successful response
	if err := p.updateRequestSuccess(ctx, req, statusCode, body, headers); err != nil {
		return fmt.Errorf("failed to update request with response: %w", err)
	}

	return nil
}

// updateRequestProcessing updates the request status to processing
func (p *EdgeRequestProcessor) updateRequestProcessing(ctx context.Context, req *QueuedRequest) error {
	col := p.db.Collection("queued_requests")
	
	now := time.Now()
	update := bson.M{
		"$set": bson.M{
			"status":     StatusProcessing,
			"started_at": now,
		},
	}
	
	_, err := col.UpdateOne(ctx, bson.M{"_id": req.ID}, update)
	return err
}

// updateRequestError updates the request with error information
func (p *EdgeRequestProcessor) updateRequestError(ctx context.Context, req *QueuedRequest, reqErr error) error {
	col := p.db.Collection("queued_requests")
	
	now := time.Now()
	update := bson.M{
		"$set": bson.M{
			"status":       StatusFailed,
			"last_error":   reqErr.Error(),
			"completed_at": now,
		},
		"$inc": bson.M{
			"retry_count": 1,
		},
	}
	
	_, err := col.UpdateOne(ctx, bson.M{"_id": req.ID}, update)
	return err
}

// updateRequestSuccess updates the request with successful response
func (p *EdgeRequestProcessor) updateRequestSuccess(ctx context.Context, req *QueuedRequest, statusCode int32, body []byte, headers map[string]string) error {
	col := p.db.Collection("queued_requests")
	
	now := time.Now()
	update := bson.M{
		"$set": bson.M{
			"status":           StatusCompleted,
			"response_code":    int(statusCode),
			"response_body":    body,
			"response_headers": headers,
			"completed_at":     now,
		},
	}
	
	_, err := col.UpdateOne(ctx, bson.M{"_id": req.ID}, update)
	return err
}

// PriorityProcessor wraps a RequestProcessor with priority-based processing
type PriorityProcessor struct {
	processor RequestProcessor
}

// NewPriorityProcessor creates a new priority-aware processor
func NewPriorityProcessor(processor RequestProcessor) *PriorityProcessor {
	return &PriorityProcessor{
		processor: processor,
	}
}

// ProcessRequest processes requests with priority consideration
func (p *PriorityProcessor) ProcessRequest(ctx context.Context, req *QueuedRequest) error {
	// Add priority-based timeout adjustments
	timeout := p.getTimeoutForPriority(req.Priority)
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// Process the request using the wrapped processor
	return p.processor.ProcessRequest(ctx, req)
}

// getTimeoutForPriority returns appropriate timeout based on priority
func (p *PriorityProcessor) getTimeoutForPriority(priority Priority) time.Duration {
	switch priority {
	case PriorityCritical:
		return 30 * time.Second // Fast processing for critical requests
	case PriorityHigh:
		return 1 * time.Minute
	case PriorityNormal:
		return 2 * time.Minute
	case PriorityLow:
		return 5 * time.Minute // Longer timeout for low priority
	default:
		return 2 * time.Minute
	}
}

// BatchProcessor handles batch processing of multiple requests
type BatchProcessor struct {
	processor    RequestProcessor
	batchSize    int
	batchTimeout time.Duration
}

// NewBatchProcessor creates a new batch processor
func NewBatchProcessor(processor RequestProcessor, batchSize int, batchTimeout time.Duration) *BatchProcessor {
	return &BatchProcessor{
		processor:    processor,
		batchSize:    batchSize,
		batchTimeout: batchTimeout,
	}
}

// ProcessBatch processes multiple requests in a batch
func (p *BatchProcessor) ProcessBatch(ctx context.Context, requests []*QueuedRequest) []error {
	errors := make([]error, len(requests))
	
	// Process requests concurrently within the batch
	type result struct {
		index int
		err   error
	}
	
	resultChan := make(chan result, len(requests))
	
	for i, req := range requests {
		go func(index int, request *QueuedRequest) {
			err := p.processor.ProcessRequest(ctx, request)
			resultChan <- result{index: index, err: err}
		}(i, req)
	}
	
	// Collect results
	for i := 0; i < len(requests); i++ {
		res := <-resultChan
		errors[res.index] = res.err
	}
	
	return errors
}

// RetryProcessor handles retry logic for failed requests
type RetryProcessor struct {
	processor  RequestProcessor
	maxRetries int
	retryDelay time.Duration
}

// NewRetryProcessor creates a new retry processor
func NewRetryProcessor(processor RequestProcessor, maxRetries int, retryDelay time.Duration) *RetryProcessor {
	return &RetryProcessor{
		processor:  processor,
		maxRetries: maxRetries,
		retryDelay: retryDelay,
	}
}

// ProcessRequest processes a request with retry logic
func (p *RetryProcessor) ProcessRequest(ctx context.Context, req *QueuedRequest) error {
	var lastErr error
	
	for attempt := 0; attempt <= p.maxRetries; attempt++ {
		if attempt > 0 {
			// Wait before retry
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(p.retryDelay):
			}
		}
		
		err := p.processor.ProcessRequest(ctx, req)
		if err == nil {
			return nil // Success
		}
		
		lastErr = err
		
		// Check if we should retry based on error type
		if !p.shouldRetry(err) {
			break
		}
	}
	
	return fmt.Errorf("request failed after %d attempts: %w", p.maxRetries+1, lastErr)
}

// shouldRetry determines if an error is retryable
func (p *RetryProcessor) shouldRetry(err error) bool {
	// Add logic to determine if error is retryable
	// For now, retry most errors except context cancellation
	if err == context.Canceled || err == context.DeadlineExceeded {
		return false
	}
	return true
}

// MetricsProcessor wraps a processor with metrics collection
type MetricsProcessor struct {
	processor RequestProcessor
	// Add metrics collection fields here
}

// NewMetricsProcessor creates a new metrics-collecting processor
func NewMetricsProcessor(processor RequestProcessor) *MetricsProcessor {
	return &MetricsProcessor{
		processor: processor,
	}
}

// ProcessRequest processes a request while collecting metrics
func (p *MetricsProcessor) ProcessRequest(ctx context.Context, req *QueuedRequest) error {
	start := time.Now()
	
	err := p.processor.ProcessRequest(ctx, req)
	
	duration := time.Since(start)
	
	// Collect metrics (implement based on your metrics system)
	p.recordMetrics(req, duration, err)
	
	return err
}

// recordMetrics records processing metrics
func (p *MetricsProcessor) recordMetrics(req *QueuedRequest, duration time.Duration, err error) {
	// Implement metrics recording based on your metrics system
	// This could be Prometheus, StatsD, etc.
	
	// Example metrics to collect:
	// - Processing duration by priority
	// - Success/failure rates
	// - Queue depth
	// - Processing throughput
}
