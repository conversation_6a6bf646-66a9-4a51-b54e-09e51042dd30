package queue

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/nats-io/nats.go"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// JetStreamQueueManager implements QueueManager using NATS JetStream and MongoDB
type JetStreamQueueManager struct {
	nats         *nats.Conn
	js           nats.JetStreamContext
	mongo        *mongo.Client
	db           *mongo.Database
	streamName   string
	consumerName string
	subscription *nats.Subscription
	processor    RequestProcessor
	stopChan     chan struct{}
}

// NewJetStreamQueueManager creates a new JetStream-based queue manager
func NewJetStreamQueueManager(natsURL, mongoURL, dbName, streamName, consumerName string) (*JetStreamQueueManager, error) {
	// NATS connection
	nc, err := nats.Connect(natsURL)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to NATS: %w", err)
	}

	js, err := nc.JetStream()
	if err != nil {
		return nil, fmt.Errorf("failed to create JetStream context: %w", err)
	}

	// MongoDB connection
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	mongoClient, err := mongo.Connect(ctx, options.Client().ApplyURI(mongoURL))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MongoDB: %w", err)
	}

	db := mongoClient.Database(dbName)

	// Create indexes
	if err := createQueueIndexes(db); err != nil {
		return nil, fmt.Errorf("failed to create indexes: %w", err)
	}

	qm := &JetStreamQueueManager{
		nats:         nc,
		js:           js,
		mongo:        mongoClient,
		db:           db,
		streamName:   streamName,
		consumerName: consumerName,
		stopChan:     make(chan struct{}),
	}

	// Initialize stream
	if err := qm.initializeStream(); err != nil {
		return nil, fmt.Errorf("failed to initialize stream: %w", err)
	}

	return qm, nil
}

// createQueueIndexes creates necessary MongoDB indexes
func createQueueIndexes(db *mongo.Database) error {
	ctx := context.Background()

	// QueuedRequest indexes
	reqCol := db.Collection("queued_requests")
	reqIndexes := []mongo.IndexModel{
		{Keys: bson.D{{"status", 1}, {"priority", -1}, {"created_at", 1}}},
		{Keys: bson.D{{"serial_number", 1}}},
		{Keys: bson.D{{"created_at", 1}}},
	}
	_, err := reqCol.Indexes().CreateMany(ctx, reqIndexes)
	if err != nil {
		return err
	}

	// MessageState indexes
	msgCol := db.Collection("message_states")
	msgIndexes := []mongo.IndexModel{
		{Keys: bson.D{{"stream_name", 1}, {"sequence", 1}}},
		{Keys: bson.D{{"consumer_id", 1}, {"status", 1}}},
		{Keys: bson.D{{"ack_deadline", 1}}},
		{Keys: bson.D{{"request_id", 1}}},
	}
	_, err = msgCol.Indexes().CreateMany(ctx, msgIndexes)
	if err != nil {
		return err
	}

	// ConsumerOffset indexes
	offsetCol := db.Collection("consumer_offsets")
	_, err = offsetCol.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys:    bson.D{{"stream_name", 1}, {"consumer_name", 1}},
		Options: options.Index().SetUnique(true),
	})

	return err
}

// initializeStream creates or updates the JetStream stream
func (qm *JetStreamQueueManager) initializeStream() error {
	// Create memory-only stream in JetStream for high performance
	config := &nats.StreamConfig{
		Name:      qm.streamName,
		Subjects:  []string{fmt.Sprintf("%s.*", qm.streamName)},
		Storage:   nats.MemoryStorage,
		Replicas:  1,              // Adjust based on your cluster setup
		MaxAge:    time.Hour * 24, // Messages expire after 24 hours
		MaxMsgs:   100000,
		Retention: nats.WorkQueuePolicy,
		Discard:   nats.DiscardOld,
	}

	_, err := qm.js.AddStream(config)
	if err != nil {
		// Try updating if stream exists
		_, err = qm.js.UpdateStream(config)
		if err != nil {
			return fmt.Errorf("failed to create/update stream: %w", err)
		}
	}

	// Store stream configuration in MongoDB
	streamConfig := StreamConfig{
		ID:       qm.streamName,
		Name:     qm.streamName,
		Subjects: config.Subjects,
		Config: map[string]interface{}{
			"max_age":   config.MaxAge.String(),
			"max_msgs":  config.MaxMsgs,
			"storage":   "memory",
			"replicas":  config.Replicas,
			"retention": config.Retention.String(),
			"discard":   config.Discard.String(),
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	ctx := context.Background()
	col := qm.db.Collection("stream_configs")
	_, err = col.ReplaceOne(ctx, bson.M{"_id": qm.streamName}, streamConfig, options.Replace().SetUpsert(true))

	return err
}

// EnqueueRequest adds a request to the queue
func (qm *JetStreamQueueManager) EnqueueRequest(ctx context.Context, req *QueuedRequest) error {
	// Store request in MongoDB
	col := qm.db.Collection("queued_requests")
	_, err := col.InsertOne(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to store request in MongoDB: %w", err)
	}

	// Publish to JetStream with priority-based subject
	subject := fmt.Sprintf("%s.%s", qm.streamName, req.Priority.String())

	payload, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	msg := &nats.Msg{
		Subject: subject,
		Data:    payload,
		Header:  make(nats.Header),
	}
	msg.Header.Set("request-id", req.ID)
	msg.Header.Set("priority", req.Priority.String())
	msg.Header.Set("serial-number", req.SerialNumber)

	_, err = qm.js.PublishMsg(msg)
	if err != nil {
		return fmt.Errorf("failed to publish to JetStream: %w", err)
	}

	return nil
}

// GetRequestStatus retrieves the status of a request
func (qm *JetStreamQueueManager) GetRequestStatus(ctx context.Context, requestID string) (*QueuedResponse, error) {
	col := qm.db.Collection("queued_requests")

	var req QueuedRequest
	err := col.FindOne(ctx, bson.M{"_id": requestID}).Decode(&req)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("request not found: %s", requestID)
		}
		return nil, fmt.Errorf("failed to find request: %w", err)
	}

	response := &QueuedResponse{
		RequestID:   req.ID,
		Status:      req.Status,
		StatusCode:  int32(req.ResponseCode),
		Body:        req.ResponseBody,
		Headers:     req.ResponseHeaders,
		CreatedAt:   req.CreatedAt,
		CompletedAt: req.CompletedAt,
	}

	if req.LastError != "" {
		response.Error = req.LastError
	}

	return response, nil
}

// CancelRequest cancels a pending request
func (qm *JetStreamQueueManager) CancelRequest(ctx context.Context, requestID string) error {
	col := qm.db.Collection("queued_requests")

	update := bson.M{
		"$set": bson.M{
			"status":       StatusCancelled,
			"completed_at": time.Now(),
		},
	}

	result, err := col.UpdateOne(ctx, bson.M{"_id": requestID, "status": bson.M{"$in": []RequestStatus{StatusPending, StatusRetrying}}}, update)
	if err != nil {
		return fmt.Errorf("failed to cancel request: %w", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("request not found or cannot be cancelled: %s", requestID)
	}

	return nil
}

// StartProcessing starts processing queued requests
func (qm *JetStreamQueueManager) StartProcessing(ctx context.Context, processor RequestProcessor) error {
	qm.processor = processor

	// Create consumer for processing messages
	consumerConfig := &nats.ConsumerConfig{
		Durable:       qm.consumerName,
		AckPolicy:     nats.AckExplicitPolicy,
		DeliverPolicy: nats.DeliverAllPolicy,
		FilterSubject: fmt.Sprintf("%s.*", qm.streamName),
		MaxDeliver:    3, // Maximum redelivery attempts
		AckWait:       30 * time.Second,
	}

	_, err := qm.js.AddConsumer(qm.streamName, consumerConfig)
	if err != nil {
		return fmt.Errorf("failed to create consumer: %w", err)
	}

	// Subscribe to messages with priority handling
	sub, err := qm.js.Subscribe(
		fmt.Sprintf("%s.*", qm.streamName),
		qm.handleMessage,
		nats.Durable(qm.consumerName),
		nats.ManualAck(),
		nats.MaxDeliver(3),
	)
	if err != nil {
		return fmt.Errorf("failed to subscribe: %w", err)
	}

	qm.subscription = sub

	// Start recovery routine
	go qm.recoveryLoop(ctx)

	return nil
}

// handleMessage processes incoming messages from JetStream
func (qm *JetStreamQueueManager) handleMessage(msg *nats.Msg) {
	ctx := context.Background()

	// Parse the request
	var req QueuedRequest
	if err := json.Unmarshal(msg.Data, &req); err != nil {
		log.Printf("Failed to unmarshal request: %v", err)
		msg.Nak()
		return
	}

	// Store message state in MongoDB
	if err := qm.storeMessageState(msg, &req, "pending"); err != nil {
		log.Printf("Failed to store message state: %v", err)
		msg.Nak()
		return
	}

	// Update request status to processing
	if err := qm.updateRequestStatus(ctx, req.ID, StatusProcessing, nil); err != nil {
		log.Printf("Failed to update request status: %v", err)
		msg.Nak()
		return
	}

	// Process the request
	if err := qm.processor.ProcessRequest(ctx, &req); err != nil {
		log.Printf("Failed to process request %s: %v", req.ID, err)

		// Update request with error
		qm.updateRequestStatus(ctx, req.ID, StatusFailed, map[string]interface{}{
			"last_error":  err.Error(),
			"retry_count": req.RetryCount + 1,
		})

		qm.storeMessageState(msg, &req, "nacked")
		msg.Nak()
		return
	}

	// Mark as completed
	qm.updateRequestStatus(ctx, req.ID, StatusCompleted, map[string]interface{}{
		"completed_at": time.Now(),
	})

	qm.storeMessageState(msg, &req, "acked")
	msg.Ack()
}

// storeMessageState stores message processing state in MongoDB
func (qm *JetStreamQueueManager) storeMessageState(msg *nats.Msg, req *QueuedRequest, status string) error {
	ctx := context.Background()
	col := qm.db.Collection("message_states")

	meta, _ := msg.Metadata()

	state := MessageState{
		ID:          fmt.Sprintf("%s-%d", qm.streamName, meta.Sequence.Stream),
		StreamName:  qm.streamName,
		Subject:     msg.Subject,
		Sequence:    meta.Sequence.Stream,
		ConsumerID:  qm.consumerName,
		Status:      status,
		Payload:     msg.Data,
		Headers:     make(map[string]interface{}),
		Timestamp:   meta.Timestamp,
		AckDeadline: time.Now().Add(30 * time.Second),
		RetryCount:  0,
		RequestID:   req.ID,
	}

	// Convert NATS headers to map
	if msg.Header != nil {
		for key, values := range msg.Header {
			state.Headers[key] = values
		}
	}

	_, err := col.ReplaceOne(ctx,
		bson.M{"_id": state.ID},
		state,
		options.Replace().SetUpsert(true),
	)

	return err
}

// updateRequestStatus updates the status of a request in MongoDB
func (qm *JetStreamQueueManager) updateRequestStatus(ctx context.Context, requestID string, status RequestStatus, updates map[string]interface{}) error {
	col := qm.db.Collection("queued_requests")

	update := bson.M{"$set": bson.M{"status": status}}
	if updates != nil {
		for key, value := range updates {
			update["$set"].(bson.M)[key] = value
		}
	}

	_, err := col.UpdateOne(ctx, bson.M{"_id": requestID}, update)
	return err
}

// recoveryLoop handles recovery of unacknowledged messages
func (qm *JetStreamQueueManager) recoveryLoop(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-qm.stopChan:
			return
		case <-ticker.C:
			if err := qm.recoverUnackedMessages(); err != nil {
				log.Printf("Recovery error: %v", err)
			}
		}
	}
}

// recoverUnackedMessages finds and redelivers unacknowledged messages
func (qm *JetStreamQueueManager) recoverUnackedMessages() error {
	ctx := context.Background()
	col := qm.db.Collection("message_states")

	// Find messages past ack deadline
	filter := bson.M{
		"status":       "pending",
		"ack_deadline": bson.M{"$lt": time.Now()},
	}

	cursor, err := col.Find(ctx, filter)
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var state MessageState
		if err := cursor.Decode(&state); err != nil {
			continue
		}

		// Update retry count and redeliver
		if err := qm.redeliverMessage(&state); err != nil {
			log.Printf("Failed to redeliver message %s: %v", state.ID, err)
		}
	}

	return nil
}

// redeliverMessage handles redelivery of failed messages
func (qm *JetStreamQueueManager) redeliverMessage(state *MessageState) error {
	// Update retry count
	state.RetryCount++
	state.Status = "redelivered"
	state.AckDeadline = time.Now().Add(30 * time.Second)

	ctx := context.Background()
	col := qm.db.Collection("message_states")

	_, err := col.ReplaceOne(ctx, bson.M{"_id": state.ID}, state)
	if err != nil {
		return err
	}

	// Republish to JetStream
	_, err = qm.js.Publish(state.Subject, state.Payload)
	return err
}

// Stop gracefully stops the queue manager
func (qm *JetStreamQueueManager) Stop() error {
	close(qm.stopChan)

	if qm.subscription != nil {
		qm.subscription.Unsubscribe()
	}

	if qm.nats != nil {
		qm.nats.Close()
	}

	if qm.mongo != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		return qm.mongo.Disconnect(ctx)
	}

	return nil
}
