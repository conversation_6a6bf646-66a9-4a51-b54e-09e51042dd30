package proxy

import (
	"context"
	"time"

	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type Server struct {
	client        boxes.EdgeDeviceClient
	boxRepository boxes.BoxFinder // Changed to Box<PERSON>inder
}

func NewServer(client boxes.EdgeDeviceClient, boxRepo boxes.BoxFinder) (*Server, error) { // Changed to BoxFinder
	return &Server{
		client:        client,
		boxRepository: boxRepo,
	}, nil
}

// ProxyRequest handles looking up a box and proxying a request to it.
func (s *Server) ProxyRequest(ctx context.Context, serialNumber string, path string) (int32, []byte, map[string]string, error) {
	// Add timeout for the entire proxy operation
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Look up box by serial number
	box, err := s.boxRepository.FindBySerialNumber(ctx, serialNumber)
	if err != nil {
		return 0, nil, nil, err
	}
	if box == nil {
		return 0, nil, nil, status.Errorf(codes.NotFound, "box not found: %s", serialNumber)
	}

	// Translate VPN address if needed
	vpnAddr, err := boxes.ToDatacenterIP(box.VPNAddress, box.Datacenter)
	if err != nil {
		return 0, nil, nil, status.Errorf(codes.Internal, "failed to translate VPN address: %v", err)
	}

	// Make the request to the box with remaining context
	resp, err := s.client.Request(ctx, vpnAddr, boxes.Restbin, path)
	if err != nil {
		return 0, nil, nil, status.Errorf(codes.Internal, "failed to contact box: %v", err)
	}

	return int32(resp.StatusCode), resp.Body, resp.Headers, nil
}
