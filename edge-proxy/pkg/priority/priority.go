package priority

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/EPIKio/myepikV2/edge/pkg/queue"
)

// RequestClassifier determines the priority of requests based on various factors
type RequestClassifier struct {
	rules []PriorityRule
}

// PriorityRule defines a rule for determining request priority
type PriorityRule struct {
	Name        string
	Condition   func(*queue.QueuedRequest) bool
	Priority    queue.Priority
	Description string
}

// NewRequestClassifier creates a new request classifier with default rules
func NewRequestClassifier() *RequestClassifier {
	classifier := &RequestClassifier{
		rules: make([]PriorityRule, 0),
	}
	
	// Add default priority rules
	classifier.AddDefaultRules()
	
	return classifier
}

// AddDefaultRules adds the default set of priority rules
func (c *RequestClassifier) AddDefaultRules() {
	// Critical priority rules
	c.AddRule(PriorityRule{
		Name: "emergency_endpoints",
		Condition: func(req *queue.QueuedRequest) bool {
			emergencyPaths := []string{
				"/emergency",
				"/alarm",
				"/critical",
				"/shutdown",
				"/restart",
			}
			for _, path := range emergencyPaths {
				if strings.Contains(strings.ToLower(req.Path), path) {
					return true
				}
			}
			return false
		},
		Priority:    queue.PriorityCritical,
		Description: "Emergency or critical system operations",
	})

	c.AddRule(PriorityRule{
		Name: "health_checks",
		Condition: func(req *queue.QueuedRequest) bool {
			healthPaths := []string{
				"/health",
				"/ping",
				"/status",
				"/heartbeat",
			}
			for _, path := range healthPaths {
				if strings.Contains(strings.ToLower(req.Path), path) {
					return true
				}
			}
			return false
		},
		Priority:    queue.PriorityCritical,
		Description: "Health check and monitoring requests",
	})

	// High priority rules
	c.AddRule(PriorityRule{
		Name: "real_time_data",
		Condition: func(req *queue.QueuedRequest) bool {
			realTimePaths := []string{
				"/realtime",
				"/live",
				"/current",
				"/now",
			}
			for _, path := range realTimePaths {
				if strings.Contains(strings.ToLower(req.Path), path) {
					return true
				}
			}
			return false
		},
		Priority:    queue.PriorityHigh,
		Description: "Real-time data requests",
	})

	c.AddRule(PriorityRule{
		Name: "user_interactive",
		Condition: func(req *queue.QueuedRequest) bool {
			// Check if request has user interaction metadata
			if userType, exists := req.Metadata["user_type"]; exists {
				return userType == "interactive"
			}
			return false
		},
		Priority:    queue.PriorityHigh,
		Description: "User interactive requests",
	})

	// Normal priority rules
	c.AddRule(PriorityRule{
		Name: "api_requests",
		Condition: func(req *queue.QueuedRequest) bool {
			apiPaths := []string{
				"/api",
				"/v1",
				"/v2",
				"/rest",
			}
			for _, path := range apiPaths {
				if strings.HasPrefix(strings.ToLower(req.Path), path) {
					return true
				}
			}
			return false
		},
		Priority:    queue.PriorityNormal,
		Description: "Standard API requests",
	})

	// Low priority rules
	c.AddRule(PriorityRule{
		Name: "batch_operations",
		Condition: func(req *queue.QueuedRequest) bool {
			batchPaths := []string{
				"/batch",
				"/bulk",
				"/export",
				"/backup",
				"/sync",
			}
			for _, path := range batchPaths {
				if strings.Contains(strings.ToLower(req.Path), path) {
					return true
				}
			}
			return false
		},
		Priority:    queue.PriorityLow,
		Description: "Batch and bulk operations",
	})

	c.AddRule(PriorityRule{
		Name: "analytics_requests",
		Condition: func(req *queue.QueuedRequest) bool {
			analyticsPaths := []string{
				"/analytics",
				"/metrics",
				"/stats",
				"/report",
			}
			for _, path := range analyticsPaths {
				if strings.Contains(strings.ToLower(req.Path), path) {
					return true
				}
			}
			return false
		},
		Priority:    queue.PriorityLow,
		Description: "Analytics and reporting requests",
	})

	// Default rule (should be last)
	c.AddRule(PriorityRule{
		Name: "default",
		Condition: func(req *queue.QueuedRequest) bool {
			return true // Always matches
		},
		Priority:    queue.PriorityNormal,
		Description: "Default priority for unmatched requests",
	})
}

// AddRule adds a new priority rule
func (c *RequestClassifier) AddRule(rule PriorityRule) {
	c.rules = append(c.rules, rule)
}

// ClassifyRequest determines the priority of a request
func (c *RequestClassifier) ClassifyRequest(req *queue.QueuedRequest) queue.Priority {
	// If priority is already set, respect it
	if req.Priority != queue.Priority(0) {
		return req.Priority
	}

	// Apply rules in order until one matches
	for _, rule := range c.rules {
		if rule.Condition(req) {
			return rule.Priority
		}
	}

	// Fallback to normal priority
	return queue.PriorityNormal
}

// GetMatchingRules returns all rules that match a request
func (c *RequestClassifier) GetMatchingRules(req *queue.QueuedRequest) []PriorityRule {
	var matchingRules []PriorityRule
	
	for _, rule := range c.rules {
		if rule.Condition(req) {
			matchingRules = append(matchingRules, rule)
		}
	}
	
	return matchingRules
}

// PriorityScheduler manages scheduling of requests based on priority
type PriorityScheduler struct {
	queues map[queue.Priority]chan *queue.QueuedRequest
	weights map[queue.Priority]int
	stopChan chan struct{}
}

// NewPriorityScheduler creates a new priority-based scheduler
func NewPriorityScheduler() *PriorityScheduler {
	return &PriorityScheduler{
		queues: map[queue.Priority]chan *queue.QueuedRequest{
			queue.PriorityCritical: make(chan *queue.QueuedRequest, 100),
			queue.PriorityHigh:     make(chan *queue.QueuedRequest, 500),
			queue.PriorityNormal:   make(chan *queue.QueuedRequest, 1000),
			queue.PriorityLow:      make(chan *queue.QueuedRequest, 2000),
		},
		weights: map[queue.Priority]int{
			queue.PriorityCritical: 10, // Process 10 critical requests for every 1 low priority
			queue.PriorityHigh:     5,
			queue.PriorityNormal:   2,
			queue.PriorityLow:      1,
		},
		stopChan: make(chan struct{}),
	}
}

// ScheduleRequest adds a request to the appropriate priority queue
func (s *PriorityScheduler) ScheduleRequest(req *queue.QueuedRequest) error {
	priorityQueue, exists := s.queues[req.Priority]
	if !exists {
		return fmt.Errorf("unknown priority: %v", req.Priority)
	}

	select {
	case priorityQueue <- req:
		return nil
	default:
		return fmt.Errorf("priority queue full for priority: %v", req.Priority)
	}
}

// StartScheduling starts the priority-based scheduling process
func (s *PriorityScheduler) StartScheduling(ctx context.Context, processor queue.RequestProcessor) {
	go s.scheduleLoop(ctx, processor)
}

// scheduleLoop runs the main scheduling loop
func (s *PriorityScheduler) scheduleLoop(ctx context.Context, processor queue.RequestProcessor) {
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	counters := map[queue.Priority]int{
		queue.PriorityCritical: 0,
		queue.PriorityHigh:     0,
		queue.PriorityNormal:   0,
		queue.PriorityLow:      0,
	}

	for {
		select {
		case <-ctx.Done():
			return
		case <-s.stopChan:
			return
		case <-ticker.C:
			// Process requests based on weighted priority
			s.processNextRequest(ctx, processor, counters)
		}
	}
}

// processNextRequest processes the next request based on priority weights
func (s *PriorityScheduler) processNextRequest(ctx context.Context, processor queue.RequestProcessor, counters map[queue.Priority]int) {
	// Check each priority level based on weights
	priorities := []queue.Priority{
		queue.PriorityCritical,
		queue.PriorityHigh,
		queue.PriorityNormal,
		queue.PriorityLow,
	}

	for _, priority := range priorities {
		weight := s.weights[priority]
		counter := counters[priority]

		if counter < weight {
			req := s.tryGetRequest(priority)
			if req != nil {
				counters[priority]++
				go func(r *queue.QueuedRequest) {
					if err := processor.ProcessRequest(ctx, r); err != nil {
						// Handle processing error
						fmt.Printf("Error processing request %s: %v\n", r.ID, err)
					}
				}(req)
				return
			}
		}
	}

	// Reset counters if all weights are exhausted
	allExhausted := true
	for priority, weight := range s.weights {
		if counters[priority] < weight {
			allExhausted = false
			break
		}
	}

	if allExhausted {
		for priority := range counters {
			counters[priority] = 0
		}
	}
}

// tryGetRequest attempts to get a request from the specified priority queue
func (s *PriorityScheduler) tryGetRequest(priority queue.Priority) *queue.QueuedRequest {
	priorityQueue := s.queues[priority]
	
	select {
	case req := <-priorityQueue:
		return req
	default:
		return nil
	}
}

// Stop stops the scheduler
func (s *PriorityScheduler) Stop() {
	close(s.stopChan)
}

// GetQueueStats returns statistics about the priority queues
func (s *PriorityScheduler) GetQueueStats() map[queue.Priority]int {
	stats := make(map[queue.Priority]int)
	
	for priority, queue := range s.queues {
		stats[priority] = len(queue)
	}
	
	return stats
}
