package boxes

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Ensure MongoBoxRepository implements BoxFinder
var _ BoxFinder = (*MongoBoxRepository)(nil)

// MongoBoxRepository provides MongoDB-backed box lookups
type MongoBoxRepository struct {
	boxes *mongo.Collection
}

func NewBoxRepository(ctx context.Context, uri, database string) (*MongoBoxRepository, error) {
	// Add timeout for initial connection
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	client, err := mongo.Connect(ctx, options.Client().ApplyURI(uri))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MongoDB: %w", err)
	}

	// Add timeout for ping
	pingCtx, pingCancel := context.WithTimeout(ctx, 5*time.Second)
	defer pingCancel()

	if err := client.Ping(pingCtx, nil); err != nil {
		return nil, fmt.Errorf("failed to ping MongoDB: %w", err)
	}

	return &MongoBoxRepository{
		boxes: client.Database(database).Collection("epikboxes"),
	}, nil
}

// FindBySerialNumber implements BoxFinder
func (r *MongoBoxRepository) FindBySerialNumber(ctx context.Context, serialNumber string) (*Box, error) {
	// Add timeout for find operation
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	var box Box
	err := r.boxes.FindOne(ctx, bson.M{"serialNumber": serialNumber}).Decode(&box)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, fmt.Errorf("error finding box by serial number: %w", err)
	}

	return &box, nil
}
